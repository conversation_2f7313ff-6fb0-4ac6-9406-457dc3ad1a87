# 前后端联动方案（MVP）：在 LocalStorage 存储 ChatSummary Buffer与历史聊天记录，让前端传递chatSummaryBuffer的相关信息给后端

> 目标读者：产品/前端/后端工程师（MVP 阶段）。  
> 目标：**不引入后端数据库**，在浏览器 LocalStorage 中存储每个会话的聊天内容与 Chat Summary Buffer；在聊天过程中**正确携带上下文调用 FastAPI RAG 后端**；并**每隔 30 天自动清理老旧记录**。本文仅为“实施说明录”，不包含代码。

---

## 0. 范围与原则

1) 仅覆盖前端与后端之间的**数据契约**、**数据落盘策略**与**生命周期**。  
2) **存储位置**：浏览器 LocalStorage（每个域名约 5–10MB 配额，按浏览器不同略有差异）。  
3) **后端状态**：尽量无状态。后端不保存会话，仅根据请求内的上下文（摘要 + 最近消息）完成 RAG 推理并返回。  
4) **MVP 从简**：先跑通“单机、单用户、单域名”体验；未来再演进到 Cookie 登录 + 数据库持久化。

---

## 1. 名词与角色

- 会话（Session）：一次持续的对话线程，具有唯一标识（session_id）。
- 会话摘要（Chat Summary Buffer / Summary）：对早期历史消息的滚动压缩文本（“冷区”）。
- 近期消息（Recent Messages）：尚未被压缩进摘要的若干条原始消息（“热区”）。
- 会话索引（Session Index）：用于渲染会话列表的轻量清单（id、标题、最后更新时间等）。
- 分页标识（Tab Id，可选）：每个浏览器标签页的临时标识，方便多标签协作与避免重复任务。

---

## 2. 存储布局（LocalStorage 键空间规划）

为便于管理与清理，采用统一命名空间：

- 会话内容条目（每个 session 一条）：前缀 `chat:session:` 加上 session_id。  
  内容包括：摘要、近期消息、元信息（标题、创建时间、更新时间、使用模型、可选 system prompt 哈希等）。
- 会话索引清单：键名 `chat:sessions`。维护所有会话的轻量列表；用于侧边栏列表与快速打开。
- 可选：最近打开的会话指针：键名 `chat:current`。仅保存当前正在查看的 session_id。
- 可选：清理/任务协调用键（如 gc 锁、最近一次清理时间戳等）。

设计意图：
- 每个会话一条“粗粒度对象”，避免碎片化键带来的维护复杂度。
- 索引与内容分离：索引便于快速渲染列表；内容包含完整上下文，按需加载。
- 所有键统一前缀，便于后续批量清理。

---

## 3. 前后端数据契约（无状态 RAG 调用）

### 3.1 前端请求载荷应包含的要素
- 会话标识：session_id（来自 URL 或 UI 新建时生成）。
- 上下文内容：
  - 摘要（summary）：字符串。为空表示尚无摘要。
  - 近期消息（messages）：按时间从旧到新排列；仅保留最近 N 条，N 由前端策略决定（见 §4）。
- 用户当轮输入：user_input（文本）。
- 可选：
  - 内容来源提示（如数据域、教材 id、检索参数）
  - 模型/推理参数（例如温度、最大生成长度）
  - 客户端元数据（例如浏览器信息、tab_id）

### 3.2 后端响应应包含的要素
- 模型回复：assistant_output（文本）。
- 可选：RAG 证据摘要（如检索到的文档片段概览）。
- 可选：用量信息/调试信息（便于前端日志）。

设计意图：
- 后端不保管历史；所有需要的上下文由前端随每次请求携带。
- 响应最小化：至少要有模型回复；调试/证据可用于 UI 展示或诊断。

---

## 4. 前端生命周期与策略

### 4.1 新建会话
- 触发条件：用户点击「新对话」；或访问无 session_id 的聊天路由。
- 行为：
  - 生成新的 session_id；
  - 初始化一个会话对象（空摘要、空消息、默认元信息）；
  - 将 session_id 写入 URL，便于分享/刷新恢复；
  - 更新会话索引。

### 4.2 载入既有会话
- 触发条件：用户从会话列表点击进入；或 URL 携带既有 session_id。
- 行为：
  - 从 LocalStorage 读取该会话条目；
  - 渲染摘要与近期消息；
  - 更新最近打开时间。

### 4.3 发送一轮消息（用户输入 → 调用后端 → 展示回复）
- 组装载荷：携带当前会话的摘要与“最近 N 条原始消息”以及本轮用户输入。
- 调用后端：POST 至 RAG 接口。
- 更新存储：将用户输入与模型回复附加到“近期消息”；刷新会话的更新时间；必要时触发压缩。

### 4.4 摘要滚动压缩（Summary 维护）
- 背景：为控制 token 与存储体积，需将较早的“近期消息”折叠入摘要。
- 触发策略（二选一或组合）：
  - 达到条数阈值（例如近期消息超过 M 条）；
  - 达到字符/近似 token 阈值（例如超过某上限）；
  - 页面空闲或本轮请求返回后执行。
- 行为：
  - 取出需要被折叠的早期消息，与现有摘要合并为“新摘要”；
  - 将这批早期消息从“近期消息”移出，仅保留最新的若干条；
  - 写回会话对象并更新时间。

说明：在 MVP 阶段，摘要生成可以直接调用现有后端的“摘要端点”（将旧摘要 + 待折叠消息发给后端，请其返回新摘要），从而保持“前端存储、后端算子”的职责划分。

### 4.5 30 天自动清理（GC）
- 清理对象：最后更新时间超过 30 天的会话条目。
- 触发时机（建议多点触发，确保稳定）：
  - 应用启动时；
  - 页面空闲时；
  - 固定间隔（例如每 24 小时检查一次）；
  - 可选：收到后端“需要降占用”的提示时。
- 行为：
  - 扫描命名空间内的所有会话键；
  - 比较每条会话的更新时间与当前时间；
  - 对超过 30 天的会话：从 LocalStorage 删除；同时从会话索引移除；
  - 记录最近一次清理时间用于节流。

### 4.6 多标签页协作（可选）
- 目标：避免同一会话在多个标签页同时执行“摘要合并”或“清理”产生冲突。
- 策略：
  - 使用浏览器的跨标签通信（如 BroadcastChannel 或 storage 事件）广播状态变化；
  - 使用轻量锁（例如设置一个带过期的锁键）防止并发操作；
  - 收到他页的变更通知后，仅更新 UI，不重复计算。

---

## 5. 数据结构约定（以文字描述，不给代码）

### 5.1 会话索引（chat:sessions）
- 存储一个列表，列表中每个元素至少包含：
  - id（session_id，与内容条目键后缀一致）；
  - title（人类可读的标题，可取首轮用户输入的片段或手动改名）；
  - updated_at（毫秒时间戳）；
  - 可选：created_at、model、system_prompt_hash、tag 等。

### 5.2 会话内容（chat:session:<session_id>）
- 包含下列字段：
  - summary：字符串。旧历史的滚动摘要；为空表示尚未形成摘要。
  - messages：列表。按时间从旧到新，仅保存最近若干条原始消息；每条消息含角色、文本与时间戳。
  - meta：对象。至少含 created_at、updated_at；可选 title、model、数据域标识等。
- 设计目的：
  - 读写原子化：每次写入时整体覆盖会话对象，避免局部键不一致；
  - 清理友好：过期删除时，仅需删除一个键并同步索引即可。

---

## 6. 容量与性能治理

- 近期消息限制：设置合理的上限（按条数或近似 token），超过即触发摘要折叠。
- 摘要长度限制：如摘要超过一定阈值，允许再次摘要（摘要的摘要），保持可控体积。
- 会话数量限制：例如最多保留最近 N 个活跃会话，超出后优先删除最旧且 30 天未访问的会话。
- 端到端开销：LocalStorage 读/写为同步操作，建议将频繁写入合并为“每轮对话后一次写入”，避免 UI 卡顿。



---

## 11. 与后端接口的协作要点（说明性，不给代码）

- RAG 主接口：接收会话摘要与最近消息，返回模型文本回复与可选证据摘要。后端不记录会话。
- 摘要维护接口（可复用 RAG 模型或单独的轻量模型）：接收“旧摘要 + 待折叠消息”，返回“新摘要”。
- 错误码与提示：明确区分参数问题、上下文超限、模型错误、网络问题，并在前端给出可操作化的提示（例如减少近期消息、稍后重试）。

---

## 12. 典型用户旅程（序列化流程）

1) 进入聊天页（URL 携带或生成 session_id）。  
2) 从 LocalStorage 读取并渲染该会话（若不存在则初始化）。  
3) 用户输入问题；前端组装“摘要 + 最近消息 + 本轮输入”并调用后端。  
4) 收到回复后，前端将“用户输入 + 模型回复”追加到最近消息，并刷新更新时间。  
5) 若超过阈值，触发摘要维护：将较早消息折叠进摘要，刷新会话对象。  
6) 定期/启动时执行 30 天清理，删除过期会话并同步索引。

---

## 1
