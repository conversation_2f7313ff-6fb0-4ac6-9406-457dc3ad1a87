# AI Backend API 可视化界面架构设计文档

## 项目概述

本文档描述了如何为当前 FastAPI 后端项目构建一个简单美观大方的 HTML 可视化界面，让用户能够直观地使用所有 API 功能并清晰查看返回结果。

## 当前 API 分析

### 核心 API 模块

#### 1. 大纲生成模块 (`/api/v1/outline`)

- **POST /generate** - 生成文档大纲
- **GET /task/{task_id}** - 查询任务状态
- **GET /tasks** - 获取任务列表
- **DELETE /task/{task_id}** - 删除任务
- **GET /metrics** - 获取性能指标
- **GET /file/{course_id}/{course_material_id}** - 获取大纲文件

#### 2. RAG 检索模块 (`/api/v1/rag`)

- **POST /index** - 建立文档索引
- **POST /query** - RAG 问答查询
- **GET /collections** - 获取集合列表
- **DELETE /collections/{collection_name}** - 删除集合
- **GET /collections/{collection_name}/info** - 获取集合信息

#### 3. 统一课程材料处理模块 (`/api/v1/course-materials`)

- **POST /process** - 统一处理课程材料
- **GET /tasks/{task_id}/status** - 查询任务状态
- **DELETE /{course_id}/{course_material_id}** - 清理指定材料
- **DELETE /course/{course_id}** - 清理整个课程
- **GET /health** - 健康检查

#### 4. 系统基础接口

- **GET /** - 根路径信息
- **GET /health** - 系统健康检查

## 界面架构设计

### 整体布局结构

#### 1. 顶部导航栏

- 项目标题：AI Backend API 控制台
- 系统状态指示器（健康状态、在线状态）
- 快速操作按钮（刷新、设置）

#### 2. 侧边导航菜单

- **文档处理**
  - 大纲生成
  - 任务管理
  - 性能监控
- **RAG 检索**
  - 索引管理
  - 智能问答
  - 集合管理
- **课程材料**
  - 统一处理
  - 材料管理
  - 清理工具
- **系统监控**
  - 健康状态
  - API 文档

#### 3. 主内容区域

- 功能操作面板
- 结果展示区域
- 实时状态更新

### 页面功能设计

#### 1. 大纲生成页面

**操作区域：**

- 文件上传拖拽区域（支持 .md/.txt）
- 表单输入区域：
  - 课程 ID 输入框
  - 课程材料 ID 输入框
  - 材料名称输入框
  - 自定义提示词文本域
  - 模型选择下拉框
  - 精简处理开关

**结果展示区域：**

- 任务状态卡片（进行中/已完成/失败）
- 生成的大纲内容预览（Markdown 渲染）
- 处理时间和文件信息
- 下载按钮（大纲文件）

#### 2. RAG 问答页面

**操作区域：**

- 聊天界面风格的问答框
- 模式选择（检索模式/直接聊天）
- 课程 ID 过滤器
- 高级设置面板（Top-K、集合名称）

**结果展示区域：**

- 对话历史记录
- 答案内容区域
- 来源信息卡片（显示相关文档片段）
- 相似度分数可视化

#### 3. 统一处理页面

**操作区域：**

- 一站式文件处理表单
- 进度条显示处理步骤
- 实时日志输出区域

**结果展示区域：**

- 处理步骤状态指示器
- 各阶段结果预览
- 错误信息提示
- 完整结果下载

#### 4. 管理监控页面

**集合管理：**

- 集合列表表格（名称、文档数量、状态）
- 集合详情弹窗
- 批量操作工具栏

**任务监控：**

- 实时任务状态仪表板
- 性能指标图表
- 历史任务记录

### 技术实现方案

#### 1. 前端技术栈

- **基础框架：** 纯 HTML5 + CSS3 + JavaScript（ES6+）
- **UI 组件库：** Bootstrap 5 或 Tailwind CSS
- **图表库：** Chart.js 或 ECharts
- **Markdown 渲染：** marked.js
- **文件上传：** Dropzone.js
- **HTTP 请求：** Fetch API 或 Axios

#### 2. 响应式设计

- 移动端适配（响应式布局）
- 暗色/亮色主题切换
- 多语言支持预留

#### 3. 交互体验优化

- 加载状态指示器
- 错误提示友好化
- 操作确认对话框
- 快捷键支持

### 数据流设计

#### 1. API 调用流程

```
用户操作 → 表单验证 → API 请求 → 加载状态 → 结果处理 → 界面更新
```

#### 2. 实时更新机制

- 轮询方式查询任务状态
- WebSocket 连接（可选，用于实时通知）
- 本地缓存机制

#### 3. 错误处理策略

- 网络错误重试机制
- 用户友好的错误信息展示
- 操作回滚和恢复功能

### 安全性考虑

#### 1. 输入验证

- 前端表单验证
- 文件类型和大小限制
- XSS 防护

#### 2. API 安全

- CORS 配置
- 请求频率限制
- 敏感信息脱敏

### 部署方案

#### 1. 静态文件服务

- 与 FastAPI 后端集成
- 静态资源 CDN 加速
- Gzip 压缩优化

#### 2. 开发环境

- 热重载开发服务器
- 代理配置（解决跨域问题）
- 调试工具集成

## 用户体验设计

### 1. 视觉设计原则

- 简洁现代的界面风格
- 一致的色彩和字体规范
- 清晰的信息层次结构
- 直观的操作流程指引

### 2. 交互设计要点

- 一键式操作体验
- 拖拽上传文件
- 实时反馈和进度提示
- 智能表单填充和验证

### 3. 可访问性支持

- 键盘导航支持
- 屏幕阅读器兼容
- 高对比度模式
- 字体大小调节
